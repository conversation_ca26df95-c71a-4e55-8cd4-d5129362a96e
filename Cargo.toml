[package]
name = "chinese-ime"
version = "0.1.0"
edition = "2024"

[dependencies]
log = "0.4"
fern = "0.6"
chrono = "0.4"
winreg = "0.52"
parking_lot = "0.12"
toml = "0.8.9"
serde = { version = "1.0", features = ["derive"] }
strum = { version = "0.27.1", features = ["derive"] }
thiserror = "2.0.12"
log-derive = "0.4.1"
csscolorparser = { version = "0.7.2", features = ["serde"] }
rime-api = "0.12.2"
librime-sys = "0.4.0"
env_logger = "0.10"

[dependencies.windows]
version = "0.54.0"
features = [
    "implement",
    "Win32_Foundation",
    "Win32_System_Com",
    "Win32_System_SystemServices",
    "Win32_UI_WindowsAndMessaging",
    "Win32_UI_TextServices",
    "Win32_UI_Input",
    "Win32_UI_Input_KeyboardAndMouse",
    "Win32_Security",
    "Win32_System_Registry",
    "Win32_System_LibraryLoader",
    "Win32_Graphics_Gdi",
    "Win32_System_Ole",
    "Win32_System_Variant",
]

[build-dependencies]
winres = "0.1"

[lib]
crate-type = ["cdylib", "rlib"]

[patch.crates-io]
librime-sys = { path = "librime-sys" }