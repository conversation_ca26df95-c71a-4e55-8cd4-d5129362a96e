use std::{env, fs, path::PathBuf, sync::OnceLock};

use serde::Deserialize;

use crate::{DEFAULT_CONF, <PERSON><PERSON>r, IME_NAME, Result, extend::ResultExt};

// use parking_lot::{RwLock, RwLockReadGuard};
//
// static CONF: OnceLock<RwLock<Conf>> = OnceLock::new();
//
// pub fn get() -> RwLockReadGuard<'static, Conf> {
//     CONF2.get_or_init(||RwLock::new(Conf::open_or_default())).read_recursive()
// }
//
// pub fn reload() {
//     // todo check for last modified
//     let mut conf = CONF2.get().unwrap().write();
//     *conf = Conf::open_or_default();
// }

static CONF: OnceLock<Conf> = OnceLock::new();

pub fn get() -> &'static Conf {
    CONF.get_or_init(Conf::open_or_default)
}

pub fn reload() {}

#[derive(Deserialize, Debug)]
pub struct Conf {
    pub font: Font,
    pub layout: Layout,
    pub color: Color,
    pub behavior: Behavior,
    pub engine: Option<Engine>,
}

impl Default for Conf {
    fn default() -> Self {
        toml::from_str(DEFAULT_CONF).unwrap()
    }
}

impl Conf {
    pub fn open() -> Result<Conf> {
        let path = PathBuf::from(env::var("APPDATA")?)
            .join(IME_NAME)
            .join("conf.toml");
        if !path.exists() {
            fs::create_dir_all(path.parent().unwrap())?;
            fs::write(path, DEFAULT_CONF)?;
            return Ok(Conf::default());
        }
        let conf = fs::read_to_string(path)?;
        let conf = toml::from_str(&conf).map_err(|e| Error::ParseError("conf.toml", e))?;
        Ok(conf)
    }

    pub fn open_or_default() -> Conf {
        Conf::open().log_err().unwrap_or_default()
    }
}

#[derive(Deserialize, Debug)]
pub struct Font {
    pub name: String,
    pub size: i32,
}

#[derive(Deserialize, Debug)]
pub struct Color {
    pub candidate: csscolorparser::Color,
    pub index: csscolorparser::Color,
    pub background: csscolorparser::Color,
    pub clip: csscolorparser::Color,
    pub highlight: csscolorparser::Color,
    pub highlighted: csscolorparser::Color,
}

#[derive(Deserialize, Debug)]
pub struct Layout {
    pub vertical: bool,
}

#[derive(Deserialize, Debug)]
pub struct Behavior {
    pub toggle: Option<Toggle>,
    pub cjk_space: bool,
}

#[derive(Deserialize, Debug)]
pub struct Engine {
    #[serde(rename = "type")]
    pub engine_type: String,
    pub rime_config_dir: Option<String>,
}

#[derive(Deserialize, Debug, Clone, Copy)]
pub enum Toggle {
    #[serde(alias = "eisu", alias = "英数")]
    Eisu,
    #[serde(alias = "ctrl", alias = "Control", alias = "control")]
    Ctrl,
    #[serde(alias = "capslock", alias = "caps_lock")]
    CapsLock,
}

impl Default for Toggle {
    fn default() -> Self {
        Self::Ctrl
    }
}

#[test]
fn test_open() {
    let conf = get();
    println!("{conf:#?}")
}
